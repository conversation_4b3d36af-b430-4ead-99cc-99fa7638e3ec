-- 修复评论点赞功能的数据库表
-- 确保comment_likes表存在并且结构正确

-- 检查并创建comment_likes表
DO $$ 
BEGIN
    -- 检查表是否存在
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'comment_likes') THEN
        -- 创建comment_likes表
        CREATE TABLE public.comment_likes (
            id text NOT NULL,
            "commentId" text NOT NULL,
            "userId" text NULL,
            "ipAddress" text NULL,
            "createdAt" timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT comment_likes_pkey PRIMARY KEY (id),
            CONSTRAINT comment_likes_commentId_fkey FOREIGN KEY ("commentId") REFERENCES comments (id) ON UPDATE CASCADE ON DELETE CASCADE,
            CONSTRAINT comment_likes_userId_fkey FOREIGN KEY ("userId") REFERENCES users (id) ON UPDATE CASCADE ON DELETE CASCADE,
            CONSTRAINT comment_likes_user_unique UNIQUE ("commentId", "userId"),
            CONSTRAINT comment_likes_ip_unique UNIQUE ("commentId", "ipAddress"),
            CONSTRAINT comment_likes_user_or_ip_check CHECK (("userId" IS NOT NULL) OR ("ipAddress" IS NOT NULL))
        ) TABLESPACE pg_default;

        -- 创建索引以提高查询性能
        CREATE INDEX idx_comment_likes_comment_id ON public.comment_likes USING btree ("commentId");
        CREATE INDEX idx_comment_likes_user_id ON public.comment_likes USING btree ("userId");
        CREATE INDEX idx_comment_likes_ip_address ON public.comment_likes USING btree ("ipAddress");

        -- 启用行级安全策略
        ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;

        -- 创建RLS策略
        CREATE POLICY "comment_likes_select_policy" ON public.comment_likes
            FOR SELECT USING (true);

        CREATE POLICY "comment_likes_insert_policy" ON public.comment_likes
            FOR INSERT WITH CHECK (true);

        CREATE POLICY "comment_likes_update_policy" ON public.comment_likes
            FOR UPDATE USING (true);

        CREATE POLICY "comment_likes_delete_policy" ON public.comment_likes
            FOR DELETE USING (true);

        RAISE NOTICE 'comment_likes table created successfully';
    ELSE
        RAISE NOTICE 'comment_likes table already exists';
    END IF;
END $$;

-- 验证表结构
DO $$
BEGIN
    -- 检查必要的列是否存在
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'comment_likes' AND column_name = 'commentId') THEN
        RAISE EXCEPTION 'comment_likes table missing commentId column';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'comment_likes' AND column_name = 'userId') THEN
        RAISE EXCEPTION 'comment_likes table missing userId column';
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'comment_likes' AND column_name = 'ipAddress') THEN
        RAISE EXCEPTION 'comment_likes table missing ipAddress column';
    END IF;
    
    RAISE NOTICE 'comment_likes table structure verified';
END $$;

-- 测试插入和查询功能
DO $$
DECLARE
    test_comment_id text;
    test_user_id text;
    test_ip text := '127.0.0.1';
    like_count integer;
BEGIN
    -- 获取一个测试评论ID（如果存在）
    SELECT id INTO test_comment_id FROM comments LIMIT 1;
    
    IF test_comment_id IS NOT NULL THEN
        -- 测试IP点赞
        INSERT INTO comment_likes (id, "commentId", "ipAddress", "createdAt")
        VALUES (gen_random_uuid()::text, test_comment_id, test_ip, CURRENT_TIMESTAMP)
        ON CONFLICT ("commentId", "ipAddress") DO NOTHING;
        
        -- 测试查询点赞数
        SELECT COUNT(*) INTO like_count FROM comment_likes WHERE "commentId" = test_comment_id;
        
        -- 清理测试数据
        DELETE FROM comment_likes WHERE "commentId" = test_comment_id AND "ipAddress" = test_ip;
        
        RAISE NOTICE 'comment_likes functionality test passed, like_count: %', like_count;
    ELSE
        RAISE NOTICE 'No comments found for testing, but table structure is ready';
    END IF;
END $$;
