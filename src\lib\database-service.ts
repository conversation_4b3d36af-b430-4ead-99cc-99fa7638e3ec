/**
 * 数据库服务层 - 基于Supabase API的数据库操作
 * 遵循database-management.md中的最佳实践
 */

import { supabase, supabaseAdmin } from './supabase';
import { generateId } from './utils';
import type { BlogPost, BlogCategory, BlogTag, Comment, CommentLike } from '@/types';

export class DatabaseService {
  /**
   * 博客文章相关操作
   */
  static async createBlogPost(data: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>) {
    const { data: post, error } = await supabase
      .from('blog_posts')
      .insert({
        title: data.title,
        slug: data.slug,
        content: data.content,
        excerpt: data.excerpt,
        coverImage: data.coverImage,
        locale: data.locale,
        category: data.category,
        tags: data.tags,
        status: data.status,
        publishedAt: data.publishedAt,
        scheduledAt: data.scheduledAt,
        readingTime: data.readingTime,
        featured: data.featured,
        seoTitle: data.seoTitle,
        seoDescription: data.seoDescription,
        keywords: data.keywords,
        metadata: data.metadata,
      })
      .select()
      .single();

    if (error) throw error;
    return this.transformBlogPost(post);
  }

  static async getBlogPosts(options: {
    locale?: string;
    status?: string;
    category?: string;
    featured?: boolean;
    limit?: number;
    offset?: number;
    orderBy?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}) {
    let query = supabase.from('blog_posts').select('*');

    if (options.locale) {
      query = query.eq('locale', options.locale);
    }
    if (options.status) {
      query = query.eq('status', options.status);
    }
    if (options.category) {
      query = query.eq('category', options.category);
    }
    if (options.featured !== undefined) {
      query = query.eq('featured', options.featured);
    }

    // 排序
    const orderBy = options.orderBy || 'publishedAt';
    const orderDirection = options.orderDirection || 'desc';
    query = query.order(orderBy, { ascending: orderDirection === 'asc' });

    // 分页
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data?.map(post => this.transformBlogPost(post)) || [];
  }

  static async getBlogPostBySlug(slug: string, locale?: string) {
    let query = supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug);

    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.single();

    if (error) throw error;
    return this.transformBlogPost(data);
  }

  static async getBlogPostById(id: string) {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    
    return this.transformBlogPost(data);
  }

  static async updateBlogPost(id: string, data: Partial<BlogPost>) {
    const updateData: any = {};
    
    // 映射字段名（使用camelCase以匹配现有表结构）
    if (data.title !== undefined) updateData.title = data.title;
    if (data.slug !== undefined) updateData.slug = data.slug;
    if (data.content !== undefined) updateData.content = data.content;
    if (data.excerpt !== undefined) updateData.excerpt = data.excerpt;
    if (data.coverImage !== undefined) updateData.coverImage = data.coverImage;
    if (data.category !== undefined) updateData.category = data.category;
    if (data.tags !== undefined) updateData.tags = data.tags;
    if (data.status !== undefined) updateData.status = data.status;
    if (data.publishedAt !== undefined) updateData.publishedAt = data.publishedAt;
    if (data.scheduledAt !== undefined) updateData.scheduledAt = data.scheduledAt;
    if (data.featured !== undefined) updateData.featured = data.featured;
    if (data.seoTitle !== undefined) updateData.seoTitle = data.seoTitle;
    if (data.seoDescription !== undefined) updateData.seoDescription = data.seoDescription;
    if (data.keywords !== undefined) updateData.keywords = data.keywords;
    if (data.metadata !== undefined) updateData.metadata = data.metadata;

    const { data: post, error } = await supabase
      .from('blog_posts')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return this.transformBlogPost(post);
  }

  static async deleteBlogPost(id: string) {
    const { error } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async incrementViewCount(id: string) {
    const { error } = await supabase.rpc('increment_view_count', { post_id: id });
    if (error) throw error;
  }

  /**
   * 博文交互功能相关操作
   */
  static async getUserInteractionStatus(
    postId: string, 
    userId?: string, 
    ipAddress?: string
  ) {
    const { data, error } = await supabase.rpc('get_user_interaction_status', {
      post_id: postId,
      user_id: userId || null,
      ip_address: ipAddress || null,
    });

    if (error) throw error;
    return data?.[0] || { is_liked: false, is_bookmarked: false };
  }

  static async toggleLike(postId: string, userId?: string, ipAddress?: string) {
    // 检查当前状态
    const status = await this.getUserInteractionStatus(postId, userId, ipAddress);
    
    if (status.is_liked) {
      // 取消点赞
      const { error } = await supabase
        .from('user_likes')
        .delete()
        .match(userId ? 
          { postId, userId } : 
          { postId, ipAddress }
        );
      
      if (error) throw error;
      
      // 减少计数
      await supabase.rpc('decrement_like_count', { post_id: postId });
      return { isLiked: false };
    } else {
      // 添加点赞
      const { error } = await supabase
        .from('user_likes')
        .insert({
          id: generateId(),
          postId,
          userId: userId || null,
          ipAddress: userId ? null : ipAddress,
        });
      
      if (error && error.code !== '23505') throw error; // 忽略重复插入错误
      
      // 增加计数
      await supabase.rpc('increment_like_count', { post_id: postId });
      return { isLiked: true };
    }
  }

  static async toggleBookmark(postId: string, userId: string) {
    if (!userId) {
      throw new Error('User ID is required for bookmarking');
    }

    // 检查当前状态
    const { data: existing } = await supabase
      .from('user_favorites')
      .select('id')
      .eq('postId', postId)
      .eq('userId', userId)
      .single();

    if (existing) {
      // 取消收藏
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .match({ postId, userId });
      
      if (error) throw error;
      return { isBookmarked: false };
    } else {
      // 添加收藏
      const { error } = await supabase
        .from('user_favorites')
        .insert({ 
          id: generateId(),
          postId, 
          userId 
        });
      
      if (error && error.code !== '23505') throw error; // 忽略重复插入错误
      return { isBookmarked: true };
    }
  }

  static async recordShare(
    postId: string, 
    platform: string, 
    userId?: string, 
    ipAddress?: string, 
    userAgent?: string
  ) {
    // 记录分享
    const { error: insertError } = await supabase
      .from('share_records')
      .insert({
        id: generateId(),
        postId,
        userId: userId || null,
        platform,
        ipAddress,
        userAgent,
      });

    if (insertError) {
      console.warn('Failed to record share:', insertError);
    }

    // 增加分享计数
    const { error: updateError } = await supabase.rpc('increment_share_count', { 
      post_id: postId 
    });

    if (updateError) {
      console.warn('Failed to increment share count:', updateError);
    }
  }

  static async getPostStats(postId: string) {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('viewCount, likeCount, shareCount, commentCount')
      .eq('id', postId)
      .single();

    if (error) throw error;
    
    return {
      views: data.viewCount || 0,
      likes: data.likeCount || 0,
      shares: data.shareCount || 0,
      comments: data.commentCount || 0,
    };
  }

  static async recordView(postId: string, userId?: string, ipAddress?: string, userAgent?: string) {
    // 记录浏览
    const { error: insertError } = await supabase
      .from('blog_views')
      .insert({
        id: generateId(),
        postId,
        userId: userId || null,
        ipAddress,
        userAgent,
      });

    if (insertError) {
      console.warn('Failed to record view:', insertError);
    }

    // 增加浏览计数
    await this.incrementViewCount(postId);
  }

  /**
   * 博客分类相关操作
   */
  static async getBlogCategories(locale?: string) {
    let query = supabase.from('blog_categories').select('*');
    
    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.order('name');

    if (error) throw error;
    return data?.map(cat => this.transformBlogCategory(cat)) || [];
  }

  static async getBlogCategoryBySlug(slug: string, locale?: string) {
    let query = supabase
      .from('blog_categories')
      .select('*')
      .eq('slug', slug);

    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.single();

    if (error) throw error;
    return this.transformBlogCategory(data);
  }

  /**
   * 博客标签相关操作
   */
  static async getBlogTags(locale?: string) {
    let query = supabase.from('blog_tags').select('*');
    
    if (locale) {
      query = query.eq('locale', locale);
    }

    const { data, error } = await query.order('name');

    if (error) throw error;
    return data?.map(tag => this.transformBlogTag(tag)) || [];
  }

  /**
   * 搜索功能
   */
  static async searchBlogPosts(query: string, locale?: string, limit = 10) {
    let searchQuery = supabase
      .from('blog_posts')
      .select('*')
      .or(`title.ilike.%${query}%,content.ilike.%${query}%,excerpt.ilike.%${query}%`)
      .eq('status', 'PUBLISHED')
      .limit(limit);

    if (locale) {
      searchQuery = searchQuery.eq('locale', locale);
    }

    const { data, error } = await searchQuery.order('publishedAt', { ascending: false });

    if (error) throw error;
    return data?.map(post => this.transformBlogPost(post)) || [];
  }

  /**
   * 获取分类显示名称
   */
  private static getCategoryDisplayName(categorySlug: string): string {
    const categoryNames: Record<string, string> = {
      'tarot': 'Tarot',
      'astrology': 'Astrology',
      'numerology': 'Numerology',
      'crystal': 'Crystal',
      'palmistry': 'Palmistry',
      'dreams': 'Dreams',
      'general': 'General',
    };
    return categoryNames[categorySlug] || categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1);
  }

  /**
   * 数据转换方法 - 将数据库字段映射到TypeScript接口
   */
  private static transformBlogPost(post: any): BlogPost {
    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content,
      excerpt: post.excerpt,
      coverImage: post.coverImage,
      locale: post.locale,
      category: post.category,
      tags: post.tags || [],
      status: post.status,
      publishedAt: post.publishedAt ? new Date(post.publishedAt) : undefined,
      scheduledAt: post.scheduledAt ? new Date(post.scheduledAt) : undefined,
      readingTime: post.readingTime || 0,
      viewCount: post.viewCount || 0,
      likeCount: post.likeCount || 0,
      shareCount: post.shareCount || 0,
      commentCount: post.commentCount || 0,
      featured: post.featured || false,
      seoTitle: post.seoTitle,
      seoDescription: post.seoDescription,
      keywords: post.keywords || [],

      // 提供默认作者信息
      author: {
        id: 'default-author',
        name: 'Mystical Website',
        email: '<EMAIL>',
        bio: 'AI-powered mystical content creator',
        postCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      // 提供默认分类信息
      categoryData: {
        id: post.category,
        name: this.getCategoryDisplayName(post.category),
        slug: post.category,
        locale: post.locale,
        postCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      },

      metadata: post.metadata,
      createdAt: new Date(post.createdAt),
      updatedAt: new Date(post.updatedAt),
    };
  }

  private static transformBlogCategory(category: any): BlogCategory {
    return {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      color: category.color,
      icon: category.icon,
      image: category.image,
      locale: category.locale,
      postCount: category.postCount || 0,
      seoTitle: category.seoTitle,
      seoDescription: category.seoDescription,
      createdAt: new Date(category.createdAt),
      updatedAt: new Date(category.updatedAt),
    };
  }

  private static transformBlogTag(tag: any): BlogTag {
    return {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      description: tag.description,
      color: tag.color,
      locale: tag.locale,
      postCount: tag.postCount || 0,
      createdAt: new Date(tag.createdAt),
      updatedAt: new Date(tag.updatedAt),
    };
  }

  /**
   * 评论相关操作
   */
  static async createComment(data: {
    content: string;
    postId: string;
    parentId?: string | null;
    userId?: string | null;
    guestName?: string | null;
    guestEmail?: string | null;
    isApproved?: boolean;
  }) {
    const { data: comment, error } = await supabaseAdmin
      .from('comments')
      .insert({
        id: generateId(),
        content: data.content,
        postId: data.postId,
        parentId: data.parentId,
        userId: data.userId,
        guestName: data.guestName,
        guestEmail: data.guestEmail,
        isApproved: data.isApproved || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return this.transformComment(comment);
  }

  static async getCommentsByPostId(postId: string, userId?: string, ipAddress?: string): Promise<Comment[]> {
    const { data, error } = await supabaseAdmin
      .from('comments')
      .select(`
        *,
        users:userId (
          id,
          username,
          avatar
        )
      `)
      .eq('postId', postId)
      .order('createdAt', { ascending: true });

    if (error) throw error;

    // 批量获取点赞信息以提高性能
    const commentIds = data?.map(comment => comment.id) || [];
    
    // 批量获取点赞数量
    const { data: likeCounts, error: likeCountError } = await supabaseAdmin
      .from('comment_likes')
      .select('commentId')
      .in('commentId', commentIds);
    
    if (likeCountError) console.warn('Error fetching like counts:', likeCountError);
    
    // 统计每个评论的点赞数
    const likeCountMap = new Map<string, number>();
    likeCounts?.forEach(like => {
      const count = likeCountMap.get(like.commentId) || 0;
      likeCountMap.set(like.commentId, count + 1);
    });
    
    // 批量获取用户点赞状态
    let userLikesMap = new Map<string, boolean>();
    if (userId || ipAddress) {
      let query = supabaseAdmin
        .from('comment_likes')
        .select('commentId')
        .in('commentId', commentIds);

      if (userId) {
        query = query.eq('userId', userId).is('ipAddress', null);
      } else if (ipAddress) {
        query = query.eq('ipAddress', ipAddress).is('userId', null);
      }

      const { data: userLikes, error: userLikeError } = await query;

      if (userLikeError) console.warn('Error fetching user likes:', userLikeError);

      userLikes?.forEach(like => {
        userLikesMap.set(like.commentId, true);
      });
    }

    // 转换数据并添加点赞信息
    const comments = (data || []).map(comment => {
      return this.transformComment({
        ...comment,
        likeCount: likeCountMap.get(comment.id) || 0,
        isLiked: userLikesMap.get(comment.id) || false,
      });
    });

    return this.buildCommentTree(comments);
  }

  static async getCommentById(commentId: string): Promise<Comment | null> {
    const { data, error } = await supabaseAdmin
      .from('comments')
      .select(`
        *,
        users:userId (
          id,
          username,
          avatar
        )
      `)
      .eq('id', commentId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return this.transformComment(data);
  }

  static async incrementCommentCount(postId: string) {
    const { error } = await supabaseAdmin.rpc('increment_comment_count', {
      post_id: postId
    });

    if (error) throw error;
  }

  static async decrementCommentCount(postId: string) {
    const { error } = await supabaseAdmin.rpc('decrement_comment_count', {
      post_id: postId
    });

    if (error) throw error;
  }

  // 评论点赞相关方法
  static async getCommentLike(commentId: string, userId?: string, ipAddress?: string): Promise<CommentLike | null> {
    let query = supabaseAdmin
      .from('comment_likes')
      .select('*')
      .eq('commentId', commentId);

    if (userId) {
      query = query.eq('userId', userId).is('ipAddress', null);
    } else if (ipAddress) {
      query = query.eq('ipAddress', ipAddress).is('userId', null);
    } else {
      return null;
    }

    const { data, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return this.transformCommentLike(data);
  }

  static async addCommentLike(commentId: string, userId?: string, ipAddress?: string) {
    const { error } = await supabaseAdmin
      .from('comment_likes')
      .insert({
        id: generateId(),
        commentId,
        userId: userId || null,
        ipAddress: userId ? null : (ipAddress || null), // 如果有userId，ipAddress必须为null
        createdAt: new Date().toISOString(),
      });

    if (error) throw error;
  }

  static async removeCommentLike(commentId: string, userId?: string, ipAddress?: string) {
    let query = supabaseAdmin
      .from('comment_likes')
      .delete()
      .eq('commentId', commentId);

    if (userId) {
      query = query.eq('userId', userId).is('ipAddress', null);
    } else if (ipAddress) {
      query = query.eq('ipAddress', ipAddress).is('userId', null);
    }

    const { error } = await query;
    if (error) throw error;
  }

  static async getCommentLikeCount(commentId: string): Promise<number> {
    const { count, error } = await supabaseAdmin
      .from('comment_likes')
      .select('*', { count: 'exact', head: true })
      .eq('commentId', commentId);

    if (error) throw error;
    return count || 0;
  }

  // 私有方法：数据转换
  private static transformComment(comment: any): Comment {
    const user = comment.users;
    return {
      id: comment.id,
      content: comment.content,
      userId: comment.userId,
      userName: user?.username || comment.guestName || '匿名用户',
      userAvatar: user?.avatar,
      postId: comment.postId,
      parentId: comment.parentId,
      isApproved: comment.isApproved,
      createdAt: new Date(comment.createdAt),
      updatedAt: new Date(comment.updatedAt),
      guestName: comment.guestName,
      guestEmail: comment.guestEmail,
      likeCount: comment.likeCount || 0,
      isLiked: comment.isLiked || false,
    };
  }

  private static transformCommentLike(like: any): CommentLike {
    return {
      id: like.id,
      commentId: like.commentId,
      userId: like.userId,
      ipAddress: like.ipAddress,
      createdAt: new Date(like.createdAt),
    };
  }

  private static buildCommentTree(comments: Comment[]): Comment[] {
    const commentMap = new Map<string, Comment>();
    const rootComments: Comment[] = [];

    // 先创建所有评论的映射
    comments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
    });

    // 构建树结构
    comments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!;
      
      if (comment.parentId) {
        const parent = commentMap.get(comment.parentId);
        if (parent) {
          parent.replies!.push(commentWithReplies);
        }
      } else {
        rootComments.push(commentWithReplies);
      }
    });

    return rootComments;
  }
}
