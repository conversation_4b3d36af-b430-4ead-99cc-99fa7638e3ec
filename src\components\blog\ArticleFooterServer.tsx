/**
 * 服务端文章底部组件
 * 基于SSR优先原则，确保相关文章推荐对SEO友好
 */

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/types';
import { Tag } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getTranslations } from 'next-intl/server';

interface ArticleFooterServerProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
  className?: string;
  locale: string;
}

/**
 * 服务端文章底部组件
 * 基于01-frontend-design-rules.md的博客设计规范
 * 使用服务端渲染确保SEO友好
 */
export async function ArticleFooterServer({ 
  post, 
  relatedPosts, 
  className,
  locale 
}: ArticleFooterServerProps) {
  const t = await getTranslations({ locale, namespace: 'blog' });

  return (
    <footer className={cn('space-y-12', className)}>
      {/* 文章标签 */}
      {post.tags && post.tags.length > 0 && (
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
            <Tag className="w-4 h-4" />
            <span className="text-sm font-medium">标签:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag}
                href={`/${locale}/blog?tag=${encodeURIComponent(tag)}`}
                className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                  bg-gray-100 dark:bg-gray-800 
                  text-gray-700 dark:text-gray-300
                  hover:bg-gray-200 dark:hover:bg-gray-700
                  transition-colors duration-200"
              >
                {tag}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 分享按钮区域 - 保留基础HTML结构，交互功能由客户端增强 */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            分享这篇文章
          </h3>
          <div className="flex items-center gap-3">
            {/* 基础分享按钮 - 客户端会增强为实际分享功能 */}
            <button 
              className="share-button flex items-center gap-2 px-4 py-2 rounded-lg
                bg-blue-50 dark:bg-blue-900/20 
                text-blue-600 dark:text-blue-400
                hover:bg-blue-100 dark:hover:bg-blue-900/30
                transition-colors duration-200"
              data-platform="twitter"
              data-url={typeof window !== 'undefined' ? window.location.href : ''}
              data-title={post.title}
              data-text={post.excerpt || ''}
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
              Twitter
            </button>
            
            <button 
              className="share-button flex items-center gap-2 px-4 py-2 rounded-lg
                bg-blue-50 dark:bg-blue-900/20 
                text-blue-600 dark:text-blue-400
                hover:bg-blue-100 dark:hover:bg-blue-900/30
                transition-colors duration-200"
              data-platform="facebook"
              data-url={typeof window !== 'undefined' ? window.location.href : ''}
              data-title={post.title}
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              Facebook
            </button>
            
            <button 
              className="share-button flex items-center gap-2 px-4 py-2 rounded-lg
                bg-gray-50 dark:bg-gray-800 
                text-gray-600 dark:text-gray-400
                hover:bg-gray-100 dark:hover:bg-gray-700
                transition-colors duration-200"
              data-platform="copy"
              data-url={typeof window !== 'undefined' ? window.location.href : ''}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              复制链接
            </button>
          </div>
        </div>
      </div>

      {/* 相关文章推荐 - 服务端渲染确保SEO友好 */}
      {relatedPosts.length > 0 && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-12">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-8">
            相关文章推荐
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedPosts.map((relatedPost) => (
              <Link
                key={relatedPost.id}
                href={`/${locale}/blog/${relatedPost.category}/${relatedPost.slug}`}
                className="group block bg-white dark:bg-gray-800 rounded-lg shadow-sm 
                  border border-gray-200 dark:border-gray-700
                  hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600
                  transition-all duration-200"
              >
                {/* 文章封面图 */}
                {relatedPost.coverImage && (
                  <div className="aspect-video overflow-hidden rounded-t-lg">
                    <Image
                      src={relatedPost.coverImage}
                      alt={relatedPost.title}
                      width={400}
                      height={225}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                )}
                
                {/* 文章内容 */}
                <div className="p-4">
                  <div className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide mb-2">
                    {relatedPost.category}
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2
                    group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">
                    {relatedPost.title}
                  </h4>
                  {relatedPost.excerpt && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 mb-3">
                      {relatedPost.excerpt}
                    </p>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{relatedPost.readingTime} min read</span>
                    <span>{relatedPost.viewCount} views</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </footer>
  );
}
