'use client';

import React, { useEffect } from 'react';
import {
  ReadingProgress,
  ArticleInteractions,
  FocusMode,
} from '@/components/blog';
import { ShareButtonsClient } from '@/components/blog/ShareButtonsClient';
import { BlogPost } from '@/types';

interface BlogPostClientProps {
  post: BlogPost;
  siteUrl: string;
  locale: string;
  userId?: string; // 当前登录用户ID
}

export function BlogPostClient({
  post,
  userId
}: BlogPostClientProps) {
  // 页面加载时增加浏览次数
  useEffect(() => {
    const recordView = async () => {
      try {
        // 调用浏览次数增加API
        await fetch(`/api/blog/${post.id}/view`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: userId || null,
          }),
        });
      } catch (error) {
        console.warn('Failed to record view:', error);
      }
    };

    // 延迟一点时间再记录浏览，避免快速跳转的误计数
    const timer = setTimeout(recordView, 1000);

    return () => clearTimeout(timer);
  }, [post.id, userId]);
  return (
    <>
      {/* 阅读进度指示器 */}
      <ReadingProgress target="article" variant="bar" />



      {/* 右侧浮动工具栏 - 仅保留核心功能 */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block">
        <ArticleInteractions
          postId={post.id}
          initialLikes={post.likeCount}
          initialViews={post.viewCount}
          initialComments={post.commentCount}
          initialShares={post.shareCount}
          userId={userId || undefined}
          variant="floating"
          onShare={(platform) => {
            // 可以在这里添加额外的分享逻辑
            console.log(`Shared to ${platform}`);
          }}
        />
      </div>

      {/* 专注模式控制 - 右下角 */}
      <FocusMode className="fixed right-6 bottom-6 z-50" />

      {/* 分享按钮客户端增强 */}
      <ShareButtonsClient
        postId={post.id}
        onShare={(platform) => {
          console.log(`Shared to ${platform}`);
        }}
      />

    </>
  );
}
