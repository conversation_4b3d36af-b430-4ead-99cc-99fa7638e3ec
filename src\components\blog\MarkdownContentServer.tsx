/**
 * 服务端Markdown内容渲染组件
 * 基于SSR优先原则，在服务端处理Markdown内容，确保SEO友好
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { processMarkdownToHtml } from '@/lib/markdown-server';

interface MarkdownContentServerProps {
  content: string;
  className?: string;
}

/**
 * 服务端Markdown内容渲染组件
 * 基于01-frontend-design-rules.md的博客文章设计规范
 * 使用服务端渲染确保SEO友好
 */
export async function MarkdownContentServer({ 
  content, 
  className 
}: MarkdownContentServerProps) {
  // 在服务端处理Markdown内容
  const processedHtml = await processMarkdownToHtml(content);

  return (
    <article 
      className={cn(
        // 基础容器样式
        'prose prose-lg max-w-none',
        
        // 浅色主题文本颜色 - 基于博文排版规范
        'prose-headings:text-gray-900 dark:prose-headings:text-gray-50',
        'prose-h1:text-gray-900 dark:prose-h1:text-gray-50',
        'prose-h2:text-gray-800 dark:prose-h2:text-gray-100', 
        'prose-h3:text-gray-700 dark:prose-h3:text-gray-200',
        'prose-h4:text-gray-700 dark:prose-h4:text-gray-200',
        'prose-p:text-gray-700 dark:prose-p:text-gray-300',
        'prose-li:text-gray-700 dark:prose-li:text-gray-300',
        'prose-blockquote:text-gray-600 dark:prose-blockquote:text-gray-300',
        'prose-a:text-blue-600 dark:prose-a:text-blue-400',
        'prose-code:text-gray-800 dark:prose-code:text-gray-200',
        
        // 字体系统 - 多语言优化
        'font-sans',
        '[&>p:first-child]:text-gray-800 [&>p:first-child]:dark:text-gray-200', // 首段特殊样式
        
        // 响应式字号系统 - 流体排版
        'text-lg leading-relaxed', // 桌面端18px基础
        'sm:text-xl sm:leading-relaxed', // 大屏20px
        'lg:text-[22px] lg:leading-[1.6]', // 超大屏22px
        
        // 标题字号系统
        'prose-h1:text-4xl prose-h1:sm:text-5xl prose-h1:lg:text-6xl',
        'prose-h2:text-3xl prose-h2:sm:text-4xl prose-h2:lg:text-5xl',
        'prose-h3:text-2xl prose-h3:sm:text-3xl prose-h3:lg:text-4xl',
        'prose-h4:text-xl prose-h4:sm:text-2xl prose-h4:lg:text-3xl',
        
        // 行高和间距系统
        'prose-headings:leading-tight',
        'prose-h1:leading-[1.1] prose-h1:tracking-tight',
        'prose-h2:leading-[1.2] prose-h2:tracking-tight',
        'prose-h3:leading-[1.25]',
        'prose-p:leading-[1.6]',
        'prose-li:leading-[1.5]',
        
        // 垂直韵律 - 统一间距系统
        'prose-h1:mt-12 prose-h1:mb-6',
        'prose-h2:mt-12 prose-h2:mb-4',
        'prose-h3:mt-8 prose-h3:mb-3',
        'prose-h4:mt-6 prose-h4:mb-2',
        'prose-p:mb-6',
        'prose-ul:mb-6 prose-ol:mb-6',
        'prose-li:mb-2',
        'prose-blockquote:my-8',
        
        // 引用块样式
        'prose-blockquote:bg-gray-50 dark:prose-blockquote:bg-gray-800/50',
        'prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-600',
        'prose-blockquote:pl-6 prose-blockquote:pr-6 prose-blockquote:py-4',
        'prose-blockquote:rounded-r-lg',
        'prose-blockquote:not-italic',
        
        // 代码样式
        'prose-code:bg-gray-100 dark:prose-code:bg-gray-800',
        'prose-code:px-2 prose-code:py-1 prose-code:rounded',
        'prose-code:text-sm prose-code:font-mono',
        'prose-code:before:content-none prose-code:after:content-none',
        
        // 代码块样式
        'prose-pre:bg-gray-900 dark:prose-pre:bg-gray-950',
        'prose-pre:text-gray-100',
        'prose-pre:rounded-lg prose-pre:p-6',
        'prose-pre:overflow-x-auto',
        'prose-pre:text-sm prose-pre:leading-relaxed',
        
        // 列表样式
        'prose-ul:list-disc prose-ol:list-decimal',
        'prose-li:marker:text-gray-500 dark:prose-li:marker:text-gray-400',
        
        // 表格样式
        'prose-table:border-collapse',
        'prose-th:border prose-th:border-gray-200 dark:prose-th:border-gray-700',
        'prose-th:bg-gray-50 dark:prose-th:bg-gray-800',
        'prose-th:text-gray-900 dark:prose-th:text-gray-100',
        'prose-th:font-semibold prose-th:p-3',
        'prose-td:border prose-td:border-gray-200 dark:prose-td:border-gray-700',
        'prose-td:text-gray-700 dark:prose-td:text-gray-300',
        'prose-td:p-3',
        
        // 链接样式
        'prose-a:font-medium prose-a:no-underline',
        'prose-a:hover:underline prose-a:transition-colors',
        
        // 图片样式
        'prose-img:rounded-lg prose-img:shadow-md',
        'prose-img:mx-auto',
        
        // 移动端优化
        'max-sm:text-base max-sm:leading-relaxed',
        'max-sm:prose-h1:text-3xl',
        'max-sm:prose-h2:text-2xl',
        'max-sm:prose-h3:text-xl',
        'max-sm:prose-h4:text-lg',
        'max-sm:prose-p:mb-4',
        'max-sm:prose-blockquote:px-4 max-sm:prose-blockquote:py-3',
        
        className
      )}
      dangerouslySetInnerHTML={{ __html: processedHtml }}
    />
  );
}
