'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'info' | 'warning';
  duration?: number;
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right';
}

const toastIcons = {
  success: (
    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
    </svg>
  ),
  error: (
    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
    </svg>
  ),
  info: (
    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  warning: (
    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
    </svg>
  ),
};

const toastColors = {
  success: {
    iconBg: 'bg-green-100 dark:bg-green-900/30',
    iconColor: 'text-green-600 dark:text-green-400',
  },
  error: {
    iconBg: 'bg-red-100 dark:bg-red-900/30',
    iconColor: 'text-red-600 dark:text-red-400',
  },
  info: {
    iconBg: 'bg-blue-100 dark:bg-blue-900/30',
    iconColor: 'text-blue-600 dark:text-blue-400',
  },
  warning: {
    iconBg: 'bg-yellow-100 dark:bg-yellow-900/30',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
  },
};

const positionClasses = {
  'top-center': 'top-8 left-1/2 -translate-x-1/2',
  'top-right': 'top-8 right-8',
  'bottom-center': 'bottom-8 left-1/2 -translate-x-1/2',
  'bottom-right': 'bottom-8 right-8',
};

export function showToast({
  message,
  type = 'success',
  duration = 3000,
  position = 'top-center'
}: ToastProps) {
  // 移除已存在的toast
  const existingToast = document.querySelector('[data-toast]');
  if (existingToast) {
    existingToast.remove();
  }

  const toast = document.createElement('div');
  toast.setAttribute('data-toast', 'true');
  toast.className = cn(
    'fixed z-50 transform transition-all duration-500 opacity-0',
    'bg-white/90 dark:bg-gray-800/90 backdrop-blur-md',
    'text-gray-800 dark:text-gray-200',
    'px-6 py-4 rounded-2xl shadow-2xl',
    'border border-gray-200/50 dark:border-gray-700/50',
    positionClasses[position],
    position.includes('top') ? 'translate-y-4' : '-translate-y-4'
  );

  const colors = toastColors[type];
  const icon = toastIcons[type];

  toast.innerHTML = `
    <div class="flex items-center gap-3">
      <div class="w-5 h-5 rounded-full ${colors.iconBg} flex items-center justify-center flex-shrink-0">
        <div class="${colors.iconColor}">
          ${icon.props ? icon : ''}
        </div>
      </div>
      <span class="text-sm font-medium leading-relaxed">${message}</span>
    </div>
  `;

  // 修复SVG图标显示
  const iconContainer = toast.querySelector('.w-5.h-5 > div');
  if (iconContainer && icon) {
    iconContainer.innerHTML = '';
    const svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgElement.setAttribute('class', 'w-3 h-3');
    svgElement.setAttribute('fill', 'none');
    svgElement.setAttribute('stroke', 'currentColor');
    svgElement.setAttribute('viewBox', '0 0 24 24');
    
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    
    switch (type) {
      case 'success':
        path.setAttribute('stroke-linecap', 'round');
        path.setAttribute('stroke-linejoin', 'round');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('d', 'M5 13l4 4L19 7');
        break;
      case 'error':
        path.setAttribute('stroke-linecap', 'round');
        path.setAttribute('stroke-linejoin', 'round');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('d', 'M6 18L18 6M6 6l12 12');
        break;
      case 'info':
        path.setAttribute('stroke-linecap', 'round');
        path.setAttribute('stroke-linejoin', 'round');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('d', 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z');
        break;
      case 'warning':
        path.setAttribute('stroke-linecap', 'round');
        path.setAttribute('stroke-linejoin', 'round');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('d', 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z');
        break;
    }
    
    svgElement.appendChild(path);
    iconContainer.appendChild(svgElement);
  }

  document.body.appendChild(toast);

  // 显示动画
  requestAnimationFrame(() => {
    toast.style.opacity = '1';
    if (position.includes('top')) {
      toast.style.transform = position === 'top-center' ? 'translate(-50%, 0)' : 'translateY(0)';
    } else {
      toast.style.transform = position === 'bottom-center' ? 'translate(-50%, 0)' : 'translateY(0)';
    }
  });

  // 自动移除
  setTimeout(() => {
    toast.style.opacity = '0';
    if (position.includes('top')) {
      toast.style.transform = position === 'top-center' ? 'translate(-50%, -1rem)' : 'translate(0, -1rem)';
    } else {
      toast.style.transform = position === 'bottom-center' ? 'translate(-50%, 1rem)' : 'translate(0, 1rem)';
    }
    
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast);
      }
    }, 500);
  }, duration);
}