/**
 * 客户端分享按钮增强组件
 * 基于渐进增强原则，为服务端渲染的分享按钮添加交互功能
 */

'use client';

import { useEffect } from 'react';

interface ShareButtonsClientProps {
  postId: string;
  onShare?: (platform: string) => void;
}

export function ShareButtonsClient({ postId, onShare }: ShareButtonsClientProps) {
  useEffect(() => {
    const handleShareClick = async (event: Event) => {
      const target = event.target as HTMLElement;
      const button = target.closest('.share-button') as HTMLButtonElement;
      
      if (!button) return;
      
      event.preventDefault();
      
      const platform = button.dataset.platform;
      const url = button.dataset.url || window.location.href;
      const title = button.dataset.title || document.title;
      const text = button.dataset.text || '';
      
      if (!platform) return;
      
      try {
        // 记录分享事件
        await fetch(`/api/blog/${postId}/interactions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'share',
            platform,
          }),
        });
        
        // 执行分享操作
        switch (platform) {
          case 'twitter':
            window.open(
              `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
              '_blank',
              'width=550,height=420'
            );
            break;
            
          case 'facebook':
            window.open(
              `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
              '_blank',
              'width=550,height=420'
            );
            break;
            
          case 'linkedin':
            window.open(
              `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
              '_blank',
              'width=550,height=420'
            );
            break;
            
          case 'copy':
            try {
              await navigator.clipboard.writeText(url);
              // 显示复制成功提示
              const originalText = button.textContent;
              button.textContent = '已复制!';
              button.classList.add('bg-green-50', 'text-green-600');
              button.classList.remove('bg-gray-50', 'text-gray-600');
              
              setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('bg-green-50', 'text-green-600');
                button.classList.add('bg-gray-50', 'text-gray-600');
              }, 2000);
            } catch (err) {
              console.error('Failed to copy URL:', err);
              // 降级方案：选择文本
              const textArea = document.createElement('textarea');
              textArea.value = url;
              document.body.appendChild(textArea);
              textArea.select();
              document.execCommand('copy');
              document.body.removeChild(textArea);
            }
            break;
        }
        
        // 触发外部回调
        if (onShare) {
          onShare(platform);
        }
        
      } catch (error) {
        console.error('Failed to record share:', error);
        // 分享记录失败不影响用户体验，继续执行分享操作
      }
    };
    
    // 为所有分享按钮添加事件监听器
    const shareButtons = document.querySelectorAll('.share-button');
    shareButtons.forEach(button => {
      button.addEventListener('click', handleShareClick);
    });
    
    // 清理事件监听器
    return () => {
      shareButtons.forEach(button => {
        button.removeEventListener('click', handleShareClick);
      });
    };
  }, [postId, onShare]);
  
  // 这个组件不渲染任何内容，只是添加交互功能
  return null;
}
