/**
 * 服务端Markdown处理工具
 * 用于在服务端将Markdown转换为HTML，确保SEO友好
 */

import { remark } from 'remark';
import remarkGfm from 'remark-gfm';
import remarkHtml from 'remark-html';

/**
 * 移除内容中的第一个H1标题（避免与页面标题重复）
 */
function removeFirstH1(htmlContent: string): string {
  // 匹配第一个H1标签并移除
  return htmlContent.replace(/<h1[^>]*>.*?<\/h1>/i, '');
}

/**
 * 为HTML标题添加ID属性，用于锚点导航
 */
function addHeadingIds(htmlContent: string): string {
  return htmlContent.replace(
    /<h([1-6])([^>]*)>([^<]+)<\/h[1-6]>/gi,
    (match, level, attributes, title) => {
      // 检查是否已经有ID
      if (attributes.includes('id=')) {
        return match;
      }

      // 生成ID
      const id = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();

      return `<h${level}${attributes} id="${id}">${title}</h${level}>`;
    }
  );
}

/**
 * 检测内容格式
 */
export function detectContentFormat(content: string): 'markdown' | 'html' | 'text' {
  // 检查是否包含HTML标签
  if (/<[^>]+>/.test(content)) {
    return 'html';
  }
  
  // 检查是否包含Markdown语法
  if (/^#{1,6}\s/.test(content) || /\*\*.*\*\*/.test(content) || /\[.*\]\(.*\)/.test(content)) {
    return 'markdown';
  }
  
  return 'text';
}

/**
 * 服务端Markdown转HTML处理
 * 在服务端将Markdown内容转换为HTML，确保SEO友好
 */
export async function processMarkdownToHtml(content: string): Promise<string> {
  try {
    let processedHtml = '';

    // 检查内容是否已经是HTML格式
    if (content.includes('<') && content.includes('>')) {
      // 如果已经是HTML，直接使用
      processedHtml = content;
    } else {
      // 如果是Markdown，转换为HTML
      const result = await remark()
        .use(remarkGfm) // 支持GitHub Flavored Markdown
        .use(remarkHtml, { sanitize: false }) // 允许HTML标签
        .process(content);
      processedHtml = String(result);
    }

    // 移除第一个H1标题（避免与页面标题重复）
    processedHtml = removeFirstH1(processedHtml);

    // 为标题添加ID属性
    processedHtml = addHeadingIds(processedHtml);

    return processedHtml;
  } catch (error) {
    console.error('Error processing markdown:', error);
    // 降级处理：返回原始内容
    return content;
  }
}

/**
 * 计算阅读时间（分钟）
 */
export function calculateReadingTime(content: string): number {
  // 移除HTML标签
  const textContent = content.replace(/<[^>]*>/g, '');
  
  // 计算字数（中英文混合）
  const chineseChars = (textContent.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = (textContent.match(/[a-zA-Z]+/g) || []).length;
  
  // 中文按字符计算，英文按单词计算
  const totalWords = chineseChars + englishWords;
  
  // 假设阅读速度：中文500字/分钟，英文200词/分钟
  const readingSpeed = 350; // 平均阅读速度
  const minutes = Math.ceil(totalWords / readingSpeed);
  
  return Math.max(1, minutes); // 至少1分钟
}

/**
 * 提取文章摘要
 */
export function extractExcerpt(content: string, maxLength: number = 160): string {
  // 移除HTML标签
  const textContent = content.replace(/<[^>]*>/g, '');
  
  // 移除多余的空白字符
  const cleanText = textContent.replace(/\s+/g, ' ').trim();
  
  if (cleanText.length <= maxLength) {
    return cleanText;
  }
  
  // 在单词边界截断
  const truncated = cleanText.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  if (lastSpaceIndex > maxLength * 0.8) {
    return truncated.substring(0, lastSpaceIndex) + '...';
  }
  
  return truncated + '...';
}
